"""
calculator Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CalculatorDetector(BaseAppDetector):
    """相机应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.CAMERA)
    
    def get_package_names(self) -> List[str]:
        """获取相机应用包名列表"""
        return [
            "com.transsion.calculator"
        ]
    
    def get_keywords(self) -> List[str]:
        """获取相机应用关键词列表"""
        return ["calculator", "计算器"]
