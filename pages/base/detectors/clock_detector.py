"""
Clock Detector检测器
"""
import subprocess
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType
from core.logger import log


class ClockDetector(BaseAppDetector):
    """时钟应用检测器"""

    def __init__(self):
        super().__init__(AppType.CLOCK)

    def get_package_names(self) -> List[str]:
        """获取时钟应用包名列表"""
        return [
            "com.transsion.deskclock",
            "com.android.deskclock",
            "com.google.android.deskclock",
            "com.samsung.android.app.clockpackage",
            "com.huawei.deskclock",
            "com.xiaomi.deskclock",
            "com.oppo.clock",
            "com.vivo.clock",
        ]

    def get_keywords(self) -> List[str]:
        """获取时钟应用关键词列表"""
        return ["clock", "time", "alarm", "timer", "deskclock", "时钟", "闹钟"]

    def check_app_opened(self) -> bool:
        """检查时钟应用是否打开 - 增强版本，包含定时器检测"""
        try:
            log.info(f"检查{self.app_type.value}应用状态")

            # 1. 优先检查定时器通知（针对悬浮窗场景）
            if self._check_timer_notification():
                return True

            # 2. 检查活动状态
            if self._check_activity_status():
                return True

            # 3. 检查焦点窗口
            if self._check_focus_window():
                return True

            # 4. 检查进程状态
            if self._check_process_status():
                return True

            log.info(f"未检测到{self.app_type.value}应用")
            return False

        except Exception as e:
            log.error(f"检查{self.app_type.value}应用失败: {e}")
            return False

    def _check_timer_notification(self) -> bool:
        """检查定时器通知状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "notification"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                notification_output = result.stdout
                return self._analyze_timer_notification(notification_output)

        except Exception as e:
            log.debug(f"检查定时器通知失败: {e}")
        return False

    def _analyze_timer_notification(self, output: str) -> bool:
        """分析定时器通知输出"""
        packages = self.get_package_names()

        lines = output.split('\n')
        for line in lines:
            # 检查是否包含时钟应用的通知
            for package in packages:
                if package in line:
                    # 检查是否是定时器相关的通知
                    if any(keyword in line.lower() for keyword in ['timer', 'countdown', 'groupkey=timer']):
                        # 检查是否是正在进行的通知
                        if any(flag in line for flag in ['ONGOING_EVENT', 'flags=ONGOING_EVENT']):
                            # 进一步验证：检查通知是否真的活跃
                            if self._verify_active_timer_notification(line):
                                log.info(f"✅ 通过定时器通知检测到{self.app_type.value}应用: {package}")
                                return True
                            else:
                                log.debug(f"发现定时器通知但不活跃: {package}")

        return False

    def _verify_active_timer_notification(self, notification_line: str) -> bool:
        """验证定时器通知是否真的活跃"""
        try:
            # 检查通知是否包含实际的定时器内容
            # 如果通知中包含具体的倒计时时间或者活跃状态指示器，则认为是活跃的

            # 方法1：检查是否有可见的内容视图
            if "contentView=" in notification_line and "0x7f0e0041" in notification_line:
                # 进一步检查系统UI中是否真的有定时器显示
                return self._check_system_ui_timer()

            # 方法2：检查通知的重要性和可见性
            if "importance=3" in notification_line and "vis=PUBLIC" in notification_line:
                # 检查是否有实际的用户可见内容
                return self._check_visible_timer_content()

            return False

        except Exception as e:
            log.debug(f"验证活跃定时器通知失败: {e}")
            return False

    def _check_system_ui_timer(self) -> bool:
        """检查系统UI中是否有定时器显示"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                output = result.stdout
                # 检查是否有定时器相关的窗口或视图
                if any(keyword in output.lower() for keyword in ['timer', 'countdown', 'deskclock']):
                    # 检查是否是可见的窗口
                    lines = output.split('\n')
                    for line in lines:
                        if any(keyword in line.lower() for keyword in ['timer', 'countdown']) and "visible" in line.lower():
                            return True

            return False

        except Exception as e:
            log.debug(f"检查系统UI定时器失败: {e}")
            return False

    def _check_visible_timer_content(self) -> bool:
        """检查是否有可见的定时器内容"""
        try:
            # 检查状态栏是否有定时器图标或文本
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "statusbar"],
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                output = result.stdout
                # 检查状态栏中是否有活跃的定时器显示
                if any(keyword in output.lower() for keyword in ['timer', 'countdown', '倒计时']):
                    return True

            return False

        except Exception as e:
            log.debug(f"检查可见定时器内容失败: {e}")
            return False
