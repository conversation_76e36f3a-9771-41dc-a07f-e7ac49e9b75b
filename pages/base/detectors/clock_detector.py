"""
Clock Detector检测器
"""
import subprocess
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType
from core.logger import log


class ClockDetector(BaseAppDetector):
    """时钟应用检测器"""

    def __init__(self):
        super().__init__(AppType.CLOCK)

    def get_package_names(self) -> List[str]:
        """获取时钟应用包名列表"""
        return [
            "com.transsion.deskclock",
            "com.android.deskclock",
            "com.google.android.deskclock",
            "com.samsung.android.app.clockpackage",
            "com.huawei.deskclock",
            "com.xiaomi.deskclock",
            "com.oppo.clock",
            "com.vivo.clock",
        ]

    def get_keywords(self) -> List[str]:
        """获取时钟应用关键词列表"""
        return ["clock", "time", "alarm", "timer", "deskclock", "时钟", "闹钟"]

    def check_app_opened(self) -> bool:
        """检查时钟应用是否打开 - 增强版本，包含定时器检测"""
        try:
            log.info(f"检查{self.app_type.value}应用状态")

            # 1. 优先检查定时器通知（针对悬浮窗场景）
            if self._check_timer_notification():
                return True

            # 2. 检查活动状态
            if self._check_activity_status():
                return True

            # 3. 检查焦点窗口
            if self._check_focus_window():
                return True

            # 4. 检查进程状态
            if self._check_process_status():
                return True

            log.info(f"未检测到{self.app_type.value}应用")
            return False

        except Exception as e:
            log.error(f"检查{self.app_type.value}应用失败: {e}")
            return False

    def _check_timer_notification(self) -> bool:
        """检查定时器通知状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "notification"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                notification_output = result.stdout
                return self._analyze_timer_notification(notification_output)

        except Exception as e:
            log.debug(f"检查定时器通知失败: {e}")
        return False

    def _analyze_timer_notification(self, output: str) -> bool:
        """分析定时器通知输出"""
        packages = self.get_package_names()

        lines = output.split('\n')
        for line in lines:
            # 检查是否包含时钟应用的通知
            for package in packages:
                if package in line:
                    # 检查是否是定时器相关的通知
                    if any(keyword in line.lower() for keyword in ['timer', 'countdown', 'groupkey=timer', 'category=alarm']):
                        # 检查是否是正在进行的通知
                        if any(flag in line for flag in ['ONGOING_EVENT', 'flags=ONGOING_EVENT']):
                            log.info(f"✅ 通过定时器通知检测到{self.app_type.value}应用: {package}")
                            return True

        return False
