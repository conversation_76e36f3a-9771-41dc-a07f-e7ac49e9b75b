"""
简单的屏幕检测测试脚本
"""
from pages.base.system_status_checker import SystemStatusChecker

def main():
    print("=== 屏幕检测功能测试 ===")
    
    checker = SystemStatusChecker()
    
    # 1. 基本屏幕状态检测
    print("\n1. 基本屏幕状态检测:")
    is_off = checker.is_screen_off()
    is_on = checker.is_screen_on()

    print(f"   📱 is_screen_off(): {is_off} ({'已灭屏' if is_off is True else '未灭屏' if is_off is False else '未知'})")
    print(f"   📱 is_screen_on():  {is_on} ({'常亮/开启' if is_on is True else '已灭屏' if is_on is False else '未知'})")

    # 逻辑验证
    if is_off is not None and is_on is not None:
        if is_off != (not is_on):
            print("   ⚠️ 逻辑不一致！")
        else:
            print("   ✅ 逻辑一致")
    
    # 2. 详细屏幕状态
    print("\n2. 详细屏幕状态:")
    status = checker.check_screen_status()
    
    print(f"   🔆 屏幕开启: {'是' if status['screen_on'] else '否' if status['screen_on'] is not None else '未知'}")
    print(f"   🔒 屏幕锁定: {'是' if status['screen_locked'] else '否' if status['screen_locked'] is not None else '未知'}")
    print(f"   👆 可交互: {'是' if status['interactive'] else '否' if status['interactive'] is not None else '未知'}")
    print(f"   ⏰ 超时设置: {status['screen_timeout_description']}")
    print(f"   🔌 充电常亮: {status['stay_on_description']}")
    print(f"   🔋 充电状态: {status['charging_status'] or '未知'}")
    print(f"   📊 电池电量: {status['battery_level']}%" if status['battery_level'] is not None else "   📊 电池电量: 未知")
    print(f"   💡 亮度级别: {status['brightness_level'] or '未知'}")
    print(f"   ⚡ 电源状态: {status['power_state']}")
    
    print("\n✅ 屏幕检测功能测试完成")

if __name__ == "__main__":
    main()
